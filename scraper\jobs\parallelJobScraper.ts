// scraper/jobs/parallelJobScraper.ts
// Parallel job scraper with OS-based dynamic worker adjustment

import dotenv from "dotenv";
dotenv.config();

import { logger } from "../utils/logger";
import {
  sendEmailNotification,
  EmailNotificationType,
} from "../utils/emailService";
import { getPrismaClient } from "../utils/prismaClient";
import { WorkerPool } from "../workers/workerPool";
import { processJobBatch } from "../workers/jobScraperWorker";
import { JobBatch } from "../utils/types";
import { v4 as uuidv4 } from "uuid";
import { ProgressTracker } from "@/services/progressTracker";
import { getContainerMetrics } from "../utils/containerMetrics";
import os from "os";

// Type definitions
interface City {
  id: string;
  name: string;
  state: {
    id: string;
    code: string;
    name: string;
  };
}

interface Occupation {
  id: string;
  title: string;
}

// Initialize Prisma client variable
let prisma: any;
let workerPool: WorkerPool;

// Constants
const MAX_OCCUPATIONS_PER_BATCH = 5;
let maxWorkers = 1;
let batchQueue: any[] = [];
let results: any[] = [];
let batchCreationComplete = false;
let memoryMonitorInterval: NodeJS.Timeout | null = null;
let circuitBreaker: any = {
  stopMonitoring: () => {},
  recordError: () => {},
  getState: () => "closed",
};
const startTime = performance.now();

/**
 * Calculate optimal worker count based on container metrics with 5% increments
 */
async function calculateOptimalWorkers(): Promise<number> {
  try {
    // Try to get container metrics first
    const containerMetrics = await getContainerMetrics();

    let memoryUsage: number;
    let cpuCount: number;

    if (containerMetrics) {
      memoryUsage = containerMetrics.memoryUsagePercent;
      cpuCount = os.cpus().length; // Still use OS for CPU count
      logger.info(
        `🧠 Container metrics - Memory: ${memoryUsage.toFixed(2)}%, CPUs: ${cpuCount}`
      );
    } else {
      // Fallback to OS metrics if container metrics not available
      const totalMemory = os.totalmem();
      const freeMemory = os.freemem();
      const usedMemory = totalMemory - freeMemory;
      memoryUsage = (usedMemory / totalMemory) * 100;
      cpuCount = os.cpus().length;
      logger.info(
        `🧠 OS metrics (fallback) - Memory: ${memoryUsage.toFixed(2)}%, CPUs: ${cpuCount}`
      );
    }

    // Calculate workers based on 5% memory usage increments
    let optimalWorkers = 1; // Start with minimum

    if (memoryUsage < 50) {
      // Very low memory usage - use most CPUs
      optimalWorkers = Math.min(cpuCount, 6);
    } else if (memoryUsage < 55) {
      optimalWorkers = Math.min(Math.floor(cpuCount * 0.9), 5);
    } else if (memoryUsage < 60) {
      optimalWorkers = Math.min(Math.floor(cpuCount * 0.8), 4);
    } else if (memoryUsage < 65) {
      optimalWorkers = Math.min(Math.floor(cpuCount * 0.7), 4);
    } else if (memoryUsage < 70) {
      optimalWorkers = Math.min(Math.floor(cpuCount * 0.6), 3);
    } else if (memoryUsage < 75) {
      optimalWorkers = Math.min(Math.floor(cpuCount * 0.5), 3);
    } else if (memoryUsage < 80) {
      optimalWorkers = Math.min(Math.floor(cpuCount * 0.4), 2);
    } else if (memoryUsage < 85) {
      optimalWorkers = Math.min(Math.floor(cpuCount * 0.3), 2);
    } else if (memoryUsage < 90) {
      optimalWorkers = 1;
    } else {
      // Critical memory usage - abort
      throw new Error(`Critical memory usage: ${memoryUsage.toFixed(2)}%`);
    }

    logger.info(
      `⚙️ Calculated optimal workers: ${optimalWorkers} (based on ${memoryUsage.toFixed(2)}% memory usage)`
    );
    return optimalWorkers;
  } catch (error) {
    logger.error(`❌ Error calculating optimal workers: ${error}`);
    // Return safe default
    return 1;
  }
}

/**
 * Simplified parallel job scraper - just logs a message for now
 */
async function runParallelJobScraper() {
  logger.info("🔥 ENTERING runParallelJobScraper function");
  try {
    logger.info("🔥 Inside try block, about to initialize Prisma");
    // Initialize Prisma client
    prisma = await getPrismaClient("cron");
    logger.info("✅ Prisma client initialized");

    // Check system resources using container metrics
    let memoryUsage: number;

    try {
      const containerMetrics = await getContainerMetrics();

      if (containerMetrics) {
        memoryUsage = containerMetrics.memoryUsagePercent;
        logger.info(
          `🧠 Container metrics - Memory: ${memoryUsage.toFixed(2)}%`
        );
      } else {
        // Fallback to OS metrics if container metrics not available
        const totalMemory = os.totalmem();
        const freeMemory = os.freemem();
        const usedMemory = totalMemory - freeMemory;
        memoryUsage = (usedMemory / totalMemory) * 100;
        logger.info(
          `🧠 OS metrics (fallback) - Memory: ${memoryUsage.toFixed(2)}%`
        );
      }
    } catch (metricsError) {
      logger.warn(
        `⚠️ Error getting metrics: ${metricsError}, using OS fallback`
      );
      const totalMemory = os.totalmem();
      const freeMemory = os.freemem();
      const usedMemory = totalMemory - freeMemory;
      memoryUsage = (usedMemory / totalMemory) * 100;
      logger.info(
        `🧠 OS metrics (fallback) - Memory: ${memoryUsage.toFixed(2)}%`
      );
    }

    // If resources are critically constrained, exit early
    if (memoryUsage > 90) {
      logger.error(`❌ Critical memory usage: ${memoryUsage.toFixed(2)}%`);

      await sendEmailNotification(EmailNotificationType.SYSTEM_OVERLOAD, {
        timestamp: new Date().toISOString(),
        message: "Job scraper aborted due to critical memory usage",
        memoryUsage: `${memoryUsage.toFixed(2)}%`,
      }).catch((err) =>
        logger.error(`Failed to send resource constraint email: ${err}`)
      );

      throw new Error("Critical memory usage - aborting job scraper");
    }

    // Calculate optimal workers based on current resource usage
    maxWorkers = await calculateOptimalWorkers();
    logger.info(`🚀 Using ${maxWorkers} workers for parallel job scraping`);

    logger.info("🚀 Parallel job scraper running...");

    // First, let's check what states exist in the database
    const allStates = await (
      await prisma
    ).state.findMany({
      select: { id: true, name: true, code: true },
      take: 10, // Just get first 10 for debugging
    });

    logger.info(`🗺️ Found ${allStates.length} states in database:`);
    allStates.forEach((state) => {
      logger.info(`   - ${state.name} (${state.code || "no code"})`);
    });

    // Get states from the database (for now, just get one state for testing)
    const stateId = await (
      await prisma
    ).state.findFirst({
      where: { code: "CA" }, // California for testing
      select: { id: true, name: true },
    });

    if (!stateId) {
      logger.error("❌ No state found with code 'CA'");
      logger.info(
        "💡 Available states listed above. Database may need to be seeded."
      );
      return;
    }

    // Get cities from the database
    const cities = (await (
      await prisma
    ).city.findMany({
      where: {
        stateId: stateId.id,
      },
      select: {
        id: true,
        name: true,
        state: {
          select: {
            id: true,
            code: true,
            name: true,
          },
        },
      },
    })) as City[];

    if (cities.length === 0) {
      logger.error("❌ No cities found for the selected states");
      return;
    }

    logger.info(`🌎 Found ${cities.length} cities in ${stateId.name}`);

    // Get occupations from the database
    const occupations = (await (
      await prisma
    ).occupation.findMany({
      select: {
        id: true,
        title: true,
      },
      orderBy: {
        title: "asc",
      },
    })) as Occupation[];

    if (occupations.length === 0) {
      logger.error("❌ No occupations found in the database");
      return;
    }

    logger.info(`💼 Found ${occupations.length} occupations`);

    // Initialize worker pool
    workerPool = new WorkerPool({
      maxWorkers: maxWorkers,
      headless: true,
      recycleThreshold: 50,
    });

    // Declare lastProgress variable before use
    let lastProgress: {
      lastOccupationIndex: number | null;
      lastCityIndex: number | null;
      metadata: any;
      sourceType: string;
    } | null = null;

    // If we don't have a progress record yet, create one
    if (!lastProgress) {
      try {
        // Create a fresh record to begin tracking progress
        const progressRecord = await (
          await prisma
        ).scrapeProgress.create({
          data: {
            id: `progress_${Date.now()}`,
            type: "parallelJobScraper",
            lastCityIndex: 0,
            lastOccupationIndex: 0,
            metadata: JSON.stringify({
              createdAt: new Date().toISOString(),
              message: "Initialized fresh progress for parallelJobScraper",
            }),
          },
        });

        logger.info(
          `💾 Created new progress record with ID: ${progressRecord.id}`
        );

        // Initialize lastProgress with the new record
        lastProgress = {
          lastOccupationIndex: 0,
          lastCityIndex: 0,
          metadata: {
            createdAt: new Date().toISOString(),
            message: "Initialized fresh progress for parallelJobScraper",
          },
          sourceType: "parallelJobScraper",
        };
      } catch (error) {
        logger.error(`❌ Error creating progress record:`, error);
      }
    }

    let currentCityIndex = lastProgress?.lastCityIndex ?? 0;
    // Get the lastOccupationIndex from the progress record
    let lastOccupationIndex: number | null = null;

    if (
      lastProgress?.lastOccupationIndex !== undefined &&
      lastProgress?.lastOccupationIndex !== null
    ) {
      // Otherwise use the lastOccupationIndex
      const parsedIndex = Number(lastProgress.lastOccupationIndex);

      // Check if the parsed value is NaN and handle it
      if (isNaN(parsedIndex)) {
        logger.warn(`⚠️ lastOccupationIndex is NaN, setting to null`);
        lastOccupationIndex = null;
      } else {
        lastOccupationIndex = parsedIndex;
        logger.info(`💾 Using lastOccupationIndex: ${lastOccupationIndex}`);
      }
    } else {
      // Default to null if nothing is available
      // This ensures we start from the beginning of the city
      lastOccupationIndex = null;
      logger.info(
        `💾 No occupation index found, starting from the beginning of city ${currentCityIndex}`
      );
    }

    if (lastProgress) {
      if (
        lastProgress.lastCityIndex !== undefined &&
        lastProgress.lastCityIndex !== null
      ) {
        const index = lastProgress.lastCityIndex;
        if (index >= 0 && index < cities.length) {
          currentCityIndex = index;
          logger.info(
            `🔄 Resuming from city index: ${currentCityIndex} (${cities[currentCityIndex].name}), occupation index: ${lastOccupationIndex}`
          );
        } else {
          logger.warn(`⚠️ Invalid city index ${index}, starting from 0`);
        }
      } else {
        logger.info("💾 DEBUG: No lastCityIndex in progress record");
      }
    } else {
      logger.info(
        `🔄 Starting from scratch — first city: ${cities[0].name}, occupation: ${occupations[0].title}`
      );
    }

    // Inside City Loop
    while (currentCityIndex < cities.length) {
      const city = cities[currentCityIndex];
      const completedThisCity = new Set<string>(
        lastProgress?.metadata?.completedOccupationIds || []
      );

      logger.info(
        `🏢 Processing city: ${city.name}, ${city.state.code}, current occupation index: ${lastOccupationIndex}`
      );

      // Determine the starting occupation index
      let startOccupationIndex = 0;

      if (lastOccupationIndex !== null && !isNaN(lastOccupationIndex)) {
        // Try to find the occupation by ID first
        const idxById = occupations.findIndex(
          (o) => Number(o.id) === Number(lastOccupationIndex)
        );

        if (idxById >= 0) {
          // Found by ID - start from the next occupation
          startOccupationIndex = idxById + 1;
          logger.info(
            `🔁 Resuming from occupation index ${startOccupationIndex} (found by ID: ${occupations[idxById].title})`
          );

          // Mark the current occupation as completed
          completedThisCity.add(String(occupations[idxById].id));
          logger.info(
            `✅ Marked occupation ${occupations[idxById].title} (ID: ${occupations[idxById].id}) as completed`
          );
        } else {
          // If not found by ID, try to use the lastOccupationIndex directly as an index
          // This handles the case where we stored the index rather than the ID
          const numIndex = Number(lastOccupationIndex);
          if (
            !isNaN(numIndex) &&
            numIndex >= 0 &&
            numIndex < occupations.length
          ) {
            startOccupationIndex = numIndex + 1;

            // Mark the current occupation as completed
            if (numIndex >= 0 && numIndex < occupations.length) {
              completedThisCity.add(String(occupations[numIndex].id));
              logger.info(
                `✅ Marked occupation ${occupations[numIndex].title} (ID: ${occupations[numIndex].id}) as completed`
              );
            }

            if (startOccupationIndex >= occupations.length) {
              // If we've reached the end of occupations, move to the next city
              logger.info(
                `🔄 Reached end of occupations list for city ${currentCityIndex}, moving to next city`
              );
              currentCityIndex++;
              startOccupationIndex = 0;

              // IMPORTANT: Set lastOccupationIndex to null, not 0
              lastOccupationIndex = null;
              logger.info(
                `💾 Resetting lastOccupationIndex to null for next city`
              );

              // Skip this city iteration and start with the next city
              if (currentCityIndex < cities.length) {
                logger.info(
                  `🔁 Moving to city: ${cities[currentCityIndex].name}`
                );

                // Save progress with the updated city index before continuing
                await updateProgressRecord(
                  currentCityIndex,
                  null,
                  {
                    completedbatchQueue: 0,
                    successfulbatchQueue: 0,
                    totalbatchQueue: 0,
                    totalJobsFound: 0,
                    totalJobsSaved: 0,
                    timestamp: new Date().toISOString(),
                  },
                  new Set<string>() // Reset completedThisCity for the new city
                );

                continue;
              } else {
                logger.info(`🔁 Reached the end of all cities, starting over`);
                currentCityIndex = 0;

                // Save progress with the reset city index
                await updateProgressRecord(
                  0,
                  null,
                  {
                    completedbatchQueue: 0,
                    successfulbatchQueue: 0,
                    totalbatchQueue: 0,
                    totalJobsFound: 0,
                    totalJobsSaved: 0,
                    timestamp: new Date().toISOString(),
                  },
                  new Set<string>() // Reset completedThisCity for the new city
                );
              }
            } else {
              logger.info(
                `🔁 Resuming from occupation index ${startOccupationIndex} (using stored index)`
              );
            }
          } else {
            logger.info(
              `💾 Could not find occupation with ID ${lastOccupationIndex}, starting from the beginning of this city`
            );
          }
        }
      }

      // Log the current state of completedThisCity
      logger.info(
        `✅ Currently have ${completedThisCity.size} completed occupations for city ${city.name}`
      );

      // slice‑by‑index approach: workers pull the next chunk atomically
      // before spawning workers:
      const totalOcc = occupations.length;
      let nextIdx = startOccupationIndex;

      // launch one async worker loop per browser
      const workerPromises: Promise<void>[] = [];
      for (let workerId = 0; workerId < maxWorkers; workerId++) {
        workerPromises.push(
          (async () => {
            while (true) {
              // grab the next slice atomically
              const myStart = nextIdx;
              if (myStart >= totalOcc) break;
              nextIdx += MAX_OCCUPATIONS_PER_BATCH;

              // build our batch slice
              const slice = occupations
                .slice(
                  myStart,
                  Math.min(myStart + MAX_OCCUPATIONS_PER_BATCH, totalOcc)
                )
                .filter((o) => !completedThisCity.has(String(o.id)));
              if (slice.length === 0) {
                // nothing to do here, go grab the next slice
                continue;
              }

              // create and process
              const batch: JobBatch = {
                batchId: uuidv4(),
                cityId: city.id,
                cityName: city.name,
                cityIndex: currentCityIndex,
                stateId: city.state.id,
                stateCode: city.state.code || "",
                stateName: city.state.name,
                occupations: slice.map((o) => ({ id: o.id, title: o.title })),
                workerIndex: workerId,
              };

              logger.info(
                `🚀 [Worker ${workerId}] Processing ${slice.length} occupations…`
              );
              const result = await processJobBatch(workerPool, batch);

              // Add result to results array for statistics
              results.push(result);

              // mark completed IDs & update counters
              if (result.success && result.completedOccupationIds) {
                for (const id of result.completedOccupationIds) {
                  completedThisCity.add(id);
                }
              }

              // persist your progress immediately:
              await saveProgress(
                /* lastOccIdx */ myStart + slice.length - 1,
                /* cityIdx */ currentCityIndex,
                /* metadata */ {
                  timestamp: new Date().toISOString(),
                  lastBatchId: batch.batchId,
                },
                /* completedThisCity */ completedThisCity
              );
            }

            logger.info(`👋 Worker ${workerId} done for city ${city.name}`);
          })()
        );
      }

      // wait until every slice is done
      await Promise.all(workerPromises);

      // at this point you know `completedThisCity` covers all occupations

      // Check if all occupations for this city have been processed
      const currentOccupationIds = occupations.map((o) => String(o.id));
      const allOccupationsCovered = currentOccupationIds.every((id) =>
        completedThisCity.has(id)
      );

      // Log the current coverage
      const completedCount = currentOccupationIds.filter((id) =>
        completedThisCity.has(id)
      ).length;
      logger.info(
        `✅ City ${city.name} coverage: ${completedCount}/${currentOccupationIds.length} occupations completed`
      );

      if (allOccupationsCovered) {
        logger.info(
          `✅ All occupations for city ${city.name} processed. Moving to next city.`
        );
        currentCityIndex++;

        // IMPORTANT: Set lastOccupationIndex to null, not 0
        // This ensures we start from the beginning of the next city
        // Setting to 0 could be confused with occupation ID 0
        lastOccupationIndex = null;

        logger.info(
          `💾 Moving to city index ${currentCityIndex} and resetting occupation index to null`
        );

        // Save progress with the updated city index
        await updateProgressRecord(
          currentCityIndex,
          null, // Use null instead of 0 for the occupation index
          {
            completedbatchQueue: 0,
            successfulbatchQueue: 0,
            totalbatchQueue: 0,
            totalJobsFound: 0,
            totalJobsSaved: 0,
            timestamp: new Date().toISOString(),
          },
          new Set<string>() // Reset completedThisCity for the new city
        );
      } else {
        logger.info(
          `🔁 Not all occupations completed for ${city.name}, will resume remaining ones.`
        );
      }
    }

    logger.info(`📦 Created ${batchQueue.length} batches of work`);

    // Mark batch creation as complete
    batchCreationComplete = true;

    // Calculate final statistics
    let completedbatchQueue = results.length;
    let successfulbatchQueue = results.filter((r) => r.success).length;
    let totalJobsFound = results.reduce(
      (sum, r) => sum + (r.jobsFound || 0),
      0
    );
    let totalJobsSaved = results.reduce(
      (sum, r) => sum + (r.jobsSaved || 0),
      0
    );

    // Calculate and log the total execution time
    const endTime = performance.now();
    const durationMs = endTime - startTime;
    const executionTimeMinutes = (durationMs / 1000 / 60).toFixed(2);

    // Log job statistics in standardized format for easier parsing
    logger.info(
      `📊 Final results: ${completedbatchQueue} batches processed, ${successfulbatchQueue} successful, ${totalJobsFound} jobs found, ${totalJobsSaved} jobs saved`
    );
    logger.info(`⏱️ Total execution time: ${executionTimeMinutes} minutes`);

    logger.info("✅ Parallel job scraper completed successfully");
  } catch (error) {
    logger.error(`❌ Error in runParallelJobScraper:`, error);

    // Record error in circuit breaker
    circuitBreaker.recordError();

    // Log error with standardized job statistics
    const endTime = performance.now();
    const durationMs = endTime - startTime;

    logger.info(
      `📊 Error results: 0 batches processed, 0 successful, 0 jobs found, 0 jobs saved`
    );
    logger.info(
      `⏱️ Total execution time: ${(durationMs / 1000 / 60).toFixed(2)} minutes`
    );

    throw error;
  } finally {
    // Clean up memory monitoring
    if (memoryMonitorInterval) {
      clearInterval(memoryMonitorInterval);
      logger.info(`🧠 Memory monitoring stopped`);
    }

    // Stop the circuit breaker monitoring
    circuitBreaker.stopMonitoring();
    logger.info("🛑 Circuit breaker monitoring stopped");

    // Force garbage collection before exiting
    if (global.gc) {
      logger.info(`🧹 Running garbage collection before exit`);
      global.gc();
    }

    // Clean up resources
    try {
      // Shutdown worker pool
      if (workerPool) {
        await workerPool.shutdown().catch((err) => {
          logger.error("Error shutting down worker pool:", err);
        });
      }

      // Disconnect Prisma
      await (await prisma).$disconnect().catch((err: any) => {
        logger.error("Error disconnecting Prisma:", err);
      });
    } catch (cleanupError) {
      logger.error("❌ Error during cleanup:", cleanupError);
    }
  }
}

/**
 * Main function to run the job with proper cleanup
 */
async function main() {
  try {
    logger.info("🚀 Starting parallel job scraper...");
    logger.info("📍 About to call runParallelJobScraper()");

    await runParallelJobScraper();

    logger.info("✅ runParallelJobScraper() completed");
    return 0;
  } catch (error) {
    logger.error("❌ Parallel job scraper failed:", error);
    return 1;
  }
}

// Run the job if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  logger.info("Running parallelJobScraper directly...");
  main()
    .then((exitCode) => {
      process.exit(exitCode);
    })
    .catch(async (error) => {
      logger.error("Unhandled error in main:", error);
      process.exit(1);
    });
}

export { runParallelJobScraper };

// When saving progress after processing batchQueue
async function saveProgress(
  lastOccupationIndex: number | null,
  cityIndex: number | null,
  metadata: any,
  completedThisCity: Set<string>
) {
  try {
    const progressTracker = new ProgressTracker("parallelJobScraper");

    // Get occupations to check if we need to increment city index
    const occupations = await (
      await prisma
    ).occupations.findMany({
      select: {
        id: true,
        title: true,
      },
      orderBy: {
        title: "asc",
      },
    });

    logger.info(`💾 DEBUG: Total occupations found: ${occupations.length}`);
    logger.info(
      `✅ City ${cityIndex} coverage: ${completedThisCity.size}/${occupations.length} occupations`
    );

    // Use the provided occupation index
    let validOccupationIndex: number | null = null;

    if (lastOccupationIndex === null) {
      validOccupationIndex = null;
      logger.info(
        `💾 Using null occupation index to start from the beginning of the city`
      );
    } else if (isNaN(Number(lastOccupationIndex))) {
      // If it's not a number, try to find the occupation by ID
      const occupationId = String(lastOccupationIndex);
      const idxById = occupations.findIndex(
        (o: { id: any }) => String(o.id) === occupationId
      );

      if (idxById >= 0) {
        validOccupationIndex = idxById;
        logger.info(
          `💾 Found occupation index ${validOccupationIndex} for ID ${occupationId}`
        );
      } else {
        // If we can't find the occupation by ID, use a small index to avoid getting stuck
        validOccupationIndex = 0;
        logger.warn(
          `⚠️ Could not find occupation with ID ${occupationId}, using index 0 to avoid getting stuck`
        );
      }
    } else {
      // It's a number, use it directly
      validOccupationIndex = Number(lastOccupationIndex);
      logger.info(
        `💾 Using provided occupation index: ${validOccupationIndex}`
      );
    }

    const validCityIndex = isNaN(Number(cityIndex)) ? null : Number(cityIndex);

    logger.info(
      `💾 Saving progress: occupation=${validOccupationIndex}, city=${validCityIndex}`
    );

    const occupationIdsForCity = occupations.map((o: { id: any }) =>
      String(o.id)
    );
    const isAllOccupationsCovered = occupationIdsForCity.every((id: string) =>
      completedThisCity.has(id)
    );

    let updatedCityIndex = validCityIndex;
    let updatedOccupationIndex: number | null = validOccupationIndex;

    if (isAllOccupationsCovered) {
      logger.info(
        `✅ All occupations completed for city index ${validCityIndex}, moving to next city...`
      );
      updatedCityIndex = validCityIndex !== null ? validCityIndex + 1 : 0;
      updatedOccupationIndex = null; // Use null instead of 0 to avoid confusion with occupation ID 0

      // 🔄 Ensure the set is fully populated before saving (in case we missed any in processing)
      completedThisCity.clear();
      occupationIdsForCity.forEach((id: string) => completedThisCity.add(id));
    }

    // Ensure metadata has required fields
    const validMetadata = {
      ...metadata,
      timestamp: metadata.timestamp || new Date().toISOString(),
      processedBy: "parallelJobScraper",
      jobsFound: metadata.jobsFound || 0,
      totalJobsFound: metadata.totalJobsFound || 0,
      // Store the occupation index to help with resuming
      occupationIndex: updatedOccupationIndex,
      // Store the completed occupation IDs
      completedOccupationIds: Array.from(completedThisCity),
    };

    const saved = await progressTracker.updateProgress({
      lastOccupationIndex: updatedOccupationIndex,
      lastCityIndex: updatedCityIndex,
      metadata: validMetadata,
    });

    if (saved) {
      // Verify the save was successful
      const verified = await progressTracker.verifyProgressSaved({
        lastOccupationIndex: updatedOccupationIndex,
        lastCityIndex: updatedCityIndex,
      });

      if (!verified) {
        logger.warn(
          `⚠️ Progress verification failed, attempting to update existing record directly`
        );

        try {
          // Try to find and update the existing record directly
          const existingRecord = await (
            await prisma
          ).scrapeProgress.findFirst({
            where: { type: "parallelJobScraper" },
            orderBy: { updatedAt: "desc" },
          });

          if (existingRecord) {
            await (
              await prisma
            ).scrapeProgress.update({
              where: { id: existingRecord.id },
              data: {
                lastOccupationIndex: updatedOccupationIndex,
                lastCityIndex: updatedCityIndex,
                metadata: JSON.stringify({
                  ...validMetadata,
                  recoveryAttempt: true,
                  recoveryTimestamp: new Date().toISOString(),
                }),
                updatedAt: new Date(),
              },
            });
            logger.info(
              `💾 Recovery update successful for record ID: ${existingRecord.id}`
            );
          } else {
            logger.warn(`⚠️ No existing record found for recovery update`);
          }
        } catch (recoveryError) {
          logger.error(`❌ Error during recovery update: ${recoveryError}`);
        }
      }
    } else {
      logger.error(`❌ Failed to save progress`);
    }
  } catch (error) {
    logger.error(`❌ Error in saveProgress:`, error);
  }
}

// Update the progress tracking to use lastOccupationIndex instead of lastOccupationId
async function updateProgressRecord(
  cityIndex: number,
  occupationIndex: string | number | null,
  metadata: {
    completedbatchQueue: number;
    successfulbatchQueue: number;
    totalbatchQueue: number;
    totalJobsFound: number;
    totalJobsSaved: number;
    timestamp: string;
    // occupationId removed from schema
  },
  completedThisCity: Set<string> = new Set<string>()
) {
  try {
    // Get occupations to check if we need to increment city index
    const occupations = await (
      await prisma
    ).occupations.findMany({
      select: {
        id: true,
        title: true,
      },
      orderBy: {
        title: "asc",
      },
    });

    // Determine the occupation index based on either the provided index or the ID in metadata
    let validOccupationIndex: number | null = null;

    if (occupationIndex === null) {
      // If null, keep it as null to start from the beginning of the city
      validOccupationIndex = null;
      logger.info(
        `💾 No occupation index provided, keeping as null to start from the beginning of the city`
      );
    } else if (isNaN(Number(occupationIndex))) {
      // If it's not a number, try to find the occupation by ID
      const occupationId = String(occupationIndex);
      const idxById = occupations.findIndex(
        (o: { id: any }) => String(o.id) === occupationId
      );

      if (idxById >= 0) {
        validOccupationIndex = idxById;
        logger.info(
          `💾 Found occupation index ${validOccupationIndex} for ID ${occupationId}`
        );
      } else {
        validOccupationIndex = null;
        logger.warn(
          `⚠️ Could not find occupation with ID ${occupationId}, starting from the beginning of the city`
        );
      }
    } else {
      // It's a number, use it directly
      validOccupationIndex = Number(occupationIndex);
      logger.info(
        `💾 Using provided occupation index: ${validOccupationIndex}`
      );
    }

    const validCityIndex = isNaN(Number(cityIndex)) ? null : Number(cityIndex);

    // Check if we've reached the end of occupations for this city
    let updatedCityIndex = validCityIndex;
    let updatedOccupationIndex = validOccupationIndex;

    // If we have a valid occupation index and it's the last one in the list
    if (validOccupationIndex !== null && occupations.length > 0) {
      // If we're at the last occupation, we should move to the next city
      if (validOccupationIndex >= occupations.length - 1) {
        logger.info(
          `🔄 Reached the end of occupations for city ${validCityIndex}, moving to next city`
        );
        updatedCityIndex = validCityIndex !== null ? validCityIndex + 1 : 0;
        updatedOccupationIndex = null; // Reset occupation index to null for the new city
      }
    }

    // Log the completed occupations for this city
    logger.info(
      `✅ City ${validCityIndex} coverage: ${completedThisCity.size} occupations completed`
    );

    // Check if all occupations are completed for this city
    const occupationIdsForCity = occupations.map((o: { id: any }) =>
      String(o.id)
    );
    const isAllOccupationsCovered = occupationIdsForCity.every((id: string) =>
      completedThisCity.has(id)
    );

    if (isAllOccupationsCovered) {
      logger.info(
        `✅ All occupations completed for city index ${validCityIndex}, moving to next city...`
      );
      updatedCityIndex = validCityIndex !== null ? validCityIndex + 1 : 0;
      updatedOccupationIndex = null; // Use null instead of 0 to avoid confusion with occupation ID 0
    }

    // Add completedThisCity to metadata
    const updatedMetadata = {
      ...metadata,
      completedOccupationIds: Array.from(completedThisCity),
    };

    // First check if a parallelJobScraper record exists
    const existingRecord = await (
      await prisma
    ).scrapeProgress.findFirst({
      where: { type: "parallelJobScraper" },
      orderBy: { updatedAt: "desc" },
    });

    if (existingRecord) {
      logger.info(
        `💾 Updating progress: City index ${validCityIndex} -> ${updatedCityIndex}, Occupation index ${validOccupationIndex} -> ${updatedOccupationIndex}`
      );

      // Update the existing record
      await (
        await prisma
      ).scrapeProgress.update({
        where: { id: existingRecord.id },
        data: {
          lastCityIndex: updatedCityIndex,
          lastOccupationIndex: updatedOccupationIndex,
          metadata: JSON.stringify(updatedMetadata),
          updatedAt: new Date(),
        },
      });
    } else {
      // Check if any record with this type exists (might have been created by another process)
      const anyExistingRecord = await (
        await prisma
      ).scrapeProgress.findFirst({
        where: { type: "parallelJobScraper" },
      });

      if (anyExistingRecord) {
        // If a record exists, update it instead of creating a new one
        logger.info(
          `💾 Found existing record with type parallelJobScraper, updating instead of creating new one`
        );

        await (
          await prisma
        ).scrapeProgress.update({
          where: { id: anyExistingRecord.id },
          data: {
            lastCityIndex: updatedCityIndex,
            lastOccupationIndex: updatedOccupationIndex,
            metadata: JSON.stringify(updatedMetadata),
            updatedAt: new Date(),
          },
        });
      } else {
        // Create a new record only if no record with this type exists
        logger.info(
          `💾 Creating new progress record: City index ${validCityIndex} -> ${updatedCityIndex}, Occupation index ${validOccupationIndex} -> ${updatedOccupationIndex}`
        );

        await (
          await prisma
        ).scrapeProgress.create({
          data: {
            id: `progress_${Date.now()}`,
            type: "parallelJobScraper",
            lastCityIndex: updatedCityIndex,
            lastOccupationIndex: updatedOccupationIndex,
            metadata: JSON.stringify(updatedMetadata),
          },
        });
      }
    }
  } catch (error) {
    logger.error(`❌ Error updating progress record: ${error}`);
  }
}
